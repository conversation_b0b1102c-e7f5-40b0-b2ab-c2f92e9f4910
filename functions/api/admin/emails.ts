interface Env {
  NOTIFICATIONS_KV: KVNamespace;
}

interface PagesContext {
  request: Request;
  env: Env;
  params: Record<string, string>;
  data: Record<string, any>;
  next: () => Promise<Response>;
  waitUntil: (promise: Promise<any>) => void;
}

type PagesFunction<T = unknown> = (context: PagesContext & { env: T }) => Promise<Response> | Response;

export const onRequestGet: PagesFunction<Env> = async (context) => {
  const { env, request } = context;
  
  const url = new URL(request.url);
  const action = url.searchParams.get('action') || 'list';
  
  try {
    if (!env.NOTIFICATIONS_KV) {
      return new Response(JSON.stringify({
        error: 'NOTIFICATIONS_KV binding not available'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    switch (action) {
      case 'list':
        // List all email-related keys
        const allKeys = await env.NOTIFICATIONS_KV.list({ prefix: 'email' });
        const emails = [];
        
        for (const key of allKeys.keys) {
          if (key.name.startsWith('email:')) {
            const value = await env.NOTIFICATIONS_KV.get(key.name);
            if (value) {
              emails.push({
                key: key.name,
                data: JSON.parse(value)
              });
            }
          }
        }
        
        // Also get the email list
        const emailList = await env.NOTIFICATIONS_KV.get('email_list');
        
        return new Response(JSON.stringify({
          success: true,
          totalKeys: allKeys.keys.length,
          emails: emails,
          emailList: emailList ? JSON.parse(emailList) : null,
          allKeys: allKeys.keys.map(k => k.name)
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
        
      case 'stats':
        // Get statistics
        const statsKeys = await env.NOTIFICATIONS_KV.list();
        const emailKeys = statsKeys.keys.filter(k => k.name.startsWith('email:'));
        const rateLimitKeys = statsKeys.keys.filter(k => k.name.startsWith('rate_limit:'));
        
        return new Response(JSON.stringify({
          success: true,
          stats: {
            totalKeys: statsKeys.keys.length,
            emailSubscriptions: emailKeys.length,
            rateLimitEntries: rateLimitKeys.length,
            allKeyNames: statsKeys.keys.map(k => k.name)
          }
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
        
      case 'clear':
        // Clear test data (only test keys)
        const clearKeys = await env.NOTIFICATIONS_KV.list();
        let deletedCount = 0;
        
        for (const key of clearKeys.keys) {
          if (key.name === 'test-key' || 
              key.name.includes('test@') || 
              key.name.includes('debug@')) {
            await env.NOTIFICATIONS_KV.delete(key.name);
            deletedCount++;
          }
        }
        
        return new Response(JSON.stringify({
          success: true,
          message: `Deleted ${deletedCount} test keys`
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
        
      default:
        return new Response(JSON.stringify({
          error: 'Invalid action. Use: list, stats, or clear'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
    }
    
  } catch (error) {
    console.error('Error in admin emails API:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
