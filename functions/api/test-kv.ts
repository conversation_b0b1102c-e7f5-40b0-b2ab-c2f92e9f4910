interface Env {
  NOTIFICATIONS_KV: KVNamespace;
}

interface PagesContext {
  request: Request;
  env: Env;
  params: Record<string, string>;
  data: Record<string, any>;
  next: () => Promise<Response>;
  waitUntil: (promise: Promise<any>) => void;
}

type PagesFunction<T = unknown> = (context: PagesContext & { env: T }) => Promise<Response> | Response;

export const onRequestGet: PagesFunction<Env> = async (context) => {
  const { env } = context;
  
  console.log('Test KV function called');
  console.log('Environment bindings available:', Object.keys(env));
  console.log('NOTIFICATIONS_KV available:', !!env.NOTIFICATIONS_KV);
  
  try {
    if (!env.NOTIFICATIONS_KV) {
      return new Response(JSON.stringify({
        error: 'NOTIFICATIONS_KV binding not available',
        availableBindings: Object.keys(env)
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Try to write a test value
    const testKey = 'test-key';
    const testValue = JSON.stringify({
      timestamp: new Date().toISOString(),
      message: 'Test KV write'
    });

    console.log('About to write to KV with key:', testKey);
    await env.NOTIFICATIONS_KV.put(testKey, testValue);
    console.log('Successfully wrote test value to KV');

    // Try to read it back immediately
    const retrievedValue = await env.NOTIFICATIONS_KV.get(testKey);
    console.log('Successfully read test value from KV:', retrievedValue);

    // List all keys in the namespace
    const allKeys = await env.NOTIFICATIONS_KV.list();
    console.log('All keys in KV namespace:', allKeys);
    
    return new Response(JSON.stringify({
      success: true,
      message: 'KV binding is working correctly',
      testValue: retrievedValue,
      allKeys: allKeys,
      availableBindings: Object.keys(env)
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error testing KV:', error);
    return new Response(JSON.stringify({
      error: 'Error testing KV binding',
      details: error instanceof Error ? error.message : 'Unknown error',
      availableBindings: Object.keys(env)
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
